'use client';

import { Todo } from '@/types';

interface TodoItemProps {
  todo: Todo;
  onUpdate: () => void;
  onEdit?: (todo: Todo) => void;
  onDelete?: (id: number) => void;
}

export default function TodoItem({ todo, onUpdate, onEdit, onDelete }: TodoItemProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '完了';
      case 'in_progress': return '進行中';
      case 'pending': return '未着手';
      default: return '未着手';
    }
  };

  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 3: return 'bg-red-100 text-red-800';
      case 2: return 'bg-orange-100 text-orange-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getPriorityText = (priority: number) => {
    switch (priority) {
      case 3: return '高';
      case 2: return '中';
      default: return '低';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP');
  };

  const updateStatus = async (newStatus: 'pending' | 'in_progress' | 'completed') => {
    try {
      const response = await fetch(`/api/todos/${todo.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        onUpdate();
      }
    } catch (error) {
      console.error('ステータス更新エラー:', error);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('このTodoを削除してもよろしいですか？')) {
      return;
    }

    try {
      const response = await fetch(`/api/todos/${todo.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onUpdate();
      }
    } catch (error) {
      console.error('削除エラー:', error);
    }
  };

  return (
    <div className="card hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-lg font-semibold text-gray-900">{todo.title}</h3>
        <div className="flex space-x-2">
          <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(todo.priority)}`}>
            優先度{getPriorityText(todo.priority)}
          </span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(todo.status)}`}>
            {getStatusText(todo.status)}
          </span>
        </div>
      </div>

      {todo.description && (
        <p className="text-gray-600 mb-3">{todo.description}</p>
      )}

      <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
        <div className="space-x-4">
          {todo.due_date && (
            <span>期日: {formatDate(todo.due_date)}</span>
          )}
          {todo.category && (
            <span 
              className="px-2 py-1 rounded text-xs font-medium text-white"
              style={{ backgroundColor: todo.category.color }}
            >
              {todo.category.name}
            </span>
          )}
          {todo.assignee && (
            <span>担当: {todo.assignee.name}</span>
          )}
        </div>
      </div>

      <div className="flex space-x-2">
        {/* 未着手の場合：開始ボタン */}
        {todo.status === 'pending' && (
          <button
            onClick={() => updateStatus('in_progress')}
            className="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors"
          >
            開始
          </button>
        )}

        {/* 進行中の場合：完了ボタン */}
        {todo.status === 'in_progress' && (
          <button
            onClick={() => updateStatus('completed')}
            className="text-sm bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors"
          >
            完了
          </button>
        )}

        {/* 完了の場合：未着手に戻すボタン */}
        {todo.status === 'completed' && (
          <button
            onClick={() => updateStatus('pending')}
            className="text-sm bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded transition-colors"
          >
            未着手に戻す
          </button>
        )}
        <button
          onClick={() => onEdit && onEdit(todo)}
          className="text-sm bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded transition-colors"
        >
          編集
        </button>
        <button
          onClick={handleDelete}
          className="text-sm bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded transition-colors"
        >
          削除
        </button>
      </div>
    </div>
  );
} 