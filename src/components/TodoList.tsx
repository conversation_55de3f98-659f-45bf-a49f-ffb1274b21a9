'use client';

import { useState, useEffect } from 'react';
import { Todo } from '@/types';
import TodoItem from './TodoItem';
import TodoForm from './TodoForm';

export default function TodoList() {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingTodo, setEditingTodo] = useState<Todo | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'in_progress' | 'completed'>('all');

  useEffect(() => {
    fetchTodos();
  }, []);

  const fetchTodos = async () => {
    try {
      const response = await fetch('/api/todos');
      const data = await response.json();
      setTodos(data.todos);
    } catch (error) {
      console.error('Todo取得エラー:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTodoCreated = () => {
    setShowForm(false);
    setEditingTodo(null);
    fetchTodos();
  };

  const handleEditTodo = (todo: Todo) => {
    setEditingTodo(todo);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingTodo(null);
  };

  const filteredTodos = todos.filter(todo => {
    if (filter === 'all') return true;
    return todo.status === filter;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-gray-500">読み込み中...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-3xl font-bold text-gray-900">Todoリスト</h2>
          <button 
            onClick={() => setShowForm(true)}
            className="btn-primary"
          >
            新しいTodoを追加
          </button>
        </div>
        
        {/* フィルター */}
        <div className="flex space-x-2 mb-6">
          {[
            { key: 'all', label: 'すべて' },
            { key: 'pending', label: '未着手' },
            { key: 'in_progress', label: '進行中' },
            { key: 'completed', label: '完了' }
          ].map(item => (
            <button
              key={item.key}
              onClick={() => setFilter(item.key as any)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                filter === item.key
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {item.label}
            </button>
          ))}
        </div>
      </div>

      {/* Todo作成・編集フォーム */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <TodoForm
              onClose={handleCloseForm}
              onSuccess={handleTodoCreated}
              editTodo={editingTodo || undefined}
            />
          </div>
        </div>
      )}

      {/* Todo一覧 */}
      <div className="space-y-4">
        {filteredTodos.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {filter === 'all' ? 'Todoがありません' : `${filter === 'pending' ? '未着手' : filter === 'in_progress' ? '進行中' : '完了'}のTodoがありません`}
          </div>
        ) : (
          filteredTodos.map(todo => (
            <TodoItem 
              key={todo.id} 
              todo={todo} 
              onUpdate={fetchTodos}
              onEdit={handleEditTodo}
            />
          ))
        )}
      </div>
    </div>
  );
} 