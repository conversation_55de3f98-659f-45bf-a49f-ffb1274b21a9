"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mem";
exports.ids = ["vendor-chunks/mem"];
exports.modules = {

/***/ "(ssr)/./node_modules/mem/dist/index.js":
/*!****************************************!*\
  !*** ./node_modules/mem/dist/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst mimicFn = __webpack_require__(/*! mimic-fn */ \"(ssr)/./node_modules/mimic-fn/index.js\");\nconst mapAgeCleaner = __webpack_require__(/*! map-age-cleaner */ \"(ssr)/./node_modules/map-age-cleaner/dist/index.js\");\nconst decoratorInstanceMap = new WeakMap();\nconst cacheStore = new WeakMap();\n/**\n[Memoize](https://en.wikipedia.org/wiki/Memoization) functions - An optimization used to speed up consecutive function calls by caching the result of calls with identical input.\n\n@param fn - Function to be memoized.\n\n@example\n```\nimport mem = require('mem');\n\nlet i = 0;\nconst counter = () => ++i;\nconst memoized = mem(counter);\n\nmemoized('foo');\n//=> 1\n\n// Cached as it's the same arguments\nmemoized('foo');\n//=> 1\n\n// Not cached anymore as the arguments changed\nmemoized('bar');\n//=> 2\n\nmemoized('bar');\n//=> 2\n```\n*/\nconst mem = (fn, { cacheKey, cache = new Map(), maxAge } = {}) => {\n    if (typeof maxAge === 'number') {\n        // TODO: Drop after https://github.com/SamVerschueren/map-age-cleaner/issues/5\n        // @ts-expect-error\n        mapAgeCleaner(cache);\n    }\n    const memoized = function (...arguments_) {\n        const key = cacheKey ? cacheKey(arguments_) : arguments_[0];\n        const cacheItem = cache.get(key);\n        if (cacheItem) {\n            return cacheItem.data;\n        }\n        const result = fn.apply(this, arguments_);\n        cache.set(key, {\n            data: result,\n            maxAge: maxAge ? Date.now() + maxAge : Number.POSITIVE_INFINITY\n        });\n        return result;\n    };\n    mimicFn(memoized, fn, {\n        ignoreNonConfigurable: true\n    });\n    cacheStore.set(memoized, cache);\n    return memoized;\n};\n/**\n@returns A [decorator](https://github.com/tc39/proposal-decorators) to memoize class methods or static class methods.\n\n@example\n```\nimport mem = require('mem');\n\nclass Example {\n    index = 0\n\n    @mem.decorator()\n    counter() {\n        return ++this.index;\n    }\n}\n\nclass ExampleWithOptions {\n    index = 0\n\n    @mem.decorator({maxAge: 1000})\n    counter() {\n        return ++this.index;\n    }\n}\n```\n*/\nmem.decorator = (options = {}) => (target, propertyKey, descriptor) => {\n    const input = target[propertyKey];\n    if (typeof input !== 'function') {\n        throw new TypeError('The decorated value must be a function');\n    }\n    delete descriptor.value;\n    delete descriptor.writable;\n    descriptor.get = function () {\n        if (!decoratorInstanceMap.has(this)) {\n            const value = mem(input, options);\n            decoratorInstanceMap.set(this, value);\n            return value;\n        }\n        return decoratorInstanceMap.get(this);\n    };\n};\n/**\nClear all cached data of a memoized function.\n\n@param fn - Memoized function.\n*/\nmem.clear = (fn) => {\n    const cache = cacheStore.get(fn);\n    if (!cache) {\n        throw new TypeError('Can\\'t clear a function that was not memoized!');\n    }\n    if (typeof cache.clear !== 'function') {\n        throw new TypeError('The cache Map can\\'t be cleared!');\n    }\n    cache.clear();\n};\nmodule.exports = mem;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mem/dist/index.js\n");

/***/ })

};
;