"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wojtekmaj";
exports.ids = ["vendor-chunks/@wojtekmaj"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wojtekmaj/date-utils/dist/esm/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wojtekmaj/date-utils/dist/esm/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCenturyEnd: () => (/* binding */ getCenturyEnd),\n/* harmony export */   getCenturyRange: () => (/* binding */ getCenturyRange),\n/* harmony export */   getCenturyStart: () => (/* binding */ getCenturyStart),\n/* harmony export */   getDate: () => (/* binding */ getDate),\n/* harmony export */   getDayEnd: () => (/* binding */ getDayEnd),\n/* harmony export */   getDayRange: () => (/* binding */ getDayRange),\n/* harmony export */   getDayStart: () => (/* binding */ getDayStart),\n/* harmony export */   getDaysInMonth: () => (/* binding */ getDaysInMonth),\n/* harmony export */   getDecadeEnd: () => (/* binding */ getDecadeEnd),\n/* harmony export */   getDecadeRange: () => (/* binding */ getDecadeRange),\n/* harmony export */   getDecadeStart: () => (/* binding */ getDecadeStart),\n/* harmony export */   getHours: () => (/* binding */ getHours),\n/* harmony export */   getHoursMinutes: () => (/* binding */ getHoursMinutes),\n/* harmony export */   getHoursMinutesSeconds: () => (/* binding */ getHoursMinutesSeconds),\n/* harmony export */   getISOLocalDate: () => (/* binding */ getISOLocalDate),\n/* harmony export */   getISOLocalDateTime: () => (/* binding */ getISOLocalDateTime),\n/* harmony export */   getISOLocalMonth: () => (/* binding */ getISOLocalMonth),\n/* harmony export */   getMilliseconds: () => (/* binding */ getMilliseconds),\n/* harmony export */   getMinutes: () => (/* binding */ getMinutes),\n/* harmony export */   getMonth: () => (/* binding */ getMonth),\n/* harmony export */   getMonthEnd: () => (/* binding */ getMonthEnd),\n/* harmony export */   getMonthHuman: () => (/* binding */ getMonthHuman),\n/* harmony export */   getMonthRange: () => (/* binding */ getMonthRange),\n/* harmony export */   getMonthStart: () => (/* binding */ getMonthStart),\n/* harmony export */   getNextCenturyEnd: () => (/* binding */ getNextCenturyEnd),\n/* harmony export */   getNextCenturyStart: () => (/* binding */ getNextCenturyStart),\n/* harmony export */   getNextDayEnd: () => (/* binding */ getNextDayEnd),\n/* harmony export */   getNextDayStart: () => (/* binding */ getNextDayStart),\n/* harmony export */   getNextDecadeEnd: () => (/* binding */ getNextDecadeEnd),\n/* harmony export */   getNextDecadeStart: () => (/* binding */ getNextDecadeStart),\n/* harmony export */   getNextMonthEnd: () => (/* binding */ getNextMonthEnd),\n/* harmony export */   getNextMonthStart: () => (/* binding */ getNextMonthStart),\n/* harmony export */   getNextYearEnd: () => (/* binding */ getNextYearEnd),\n/* harmony export */   getNextYearStart: () => (/* binding */ getNextYearStart),\n/* harmony export */   getPreviousCenturyEnd: () => (/* binding */ getPreviousCenturyEnd),\n/* harmony export */   getPreviousCenturyStart: () => (/* binding */ getPreviousCenturyStart),\n/* harmony export */   getPreviousDayEnd: () => (/* binding */ getPreviousDayEnd),\n/* harmony export */   getPreviousDayStart: () => (/* binding */ getPreviousDayStart),\n/* harmony export */   getPreviousDecadeEnd: () => (/* binding */ getPreviousDecadeEnd),\n/* harmony export */   getPreviousDecadeStart: () => (/* binding */ getPreviousDecadeStart),\n/* harmony export */   getPreviousMonthEnd: () => (/* binding */ getPreviousMonthEnd),\n/* harmony export */   getPreviousMonthStart: () => (/* binding */ getPreviousMonthStart),\n/* harmony export */   getPreviousYearEnd: () => (/* binding */ getPreviousYearEnd),\n/* harmony export */   getPreviousYearStart: () => (/* binding */ getPreviousYearStart),\n/* harmony export */   getSeconds: () => (/* binding */ getSeconds),\n/* harmony export */   getYear: () => (/* binding */ getYear),\n/* harmony export */   getYearEnd: () => (/* binding */ getYearEnd),\n/* harmony export */   getYearRange: () => (/* binding */ getYearRange),\n/* harmony export */   getYearStart: () => (/* binding */ getYearStart)\n/* harmony export */ });\n/**\n * Utils\n */\nfunction makeGetEdgeOfNeighbor(getPeriod, getEdgeOfPeriod, defaultOffset) {\n    return function makeGetEdgeOfNeighborInternal(date, offset) {\n        if (offset === void 0) { offset = defaultOffset; }\n        var previousPeriod = getPeriod(date) + offset;\n        return getEdgeOfPeriod(previousPeriod);\n    };\n}\nfunction makeGetEnd(getBeginOfNextPeriod) {\n    return function makeGetEndInternal(date) {\n        return new Date(getBeginOfNextPeriod(date).getTime() - 1);\n    };\n}\nfunction makeGetRange(getStart, getEnd) {\n    return function makeGetRangeInternal(date) {\n        return [getStart(date), getEnd(date)];\n    };\n}\n/**\n * Simple getters - getting a property of a given point in time\n */\n/**\n * Gets year from a given date.\n *\n * @param {DateLike} date Date to get year from\n * @returns {number} Year\n */\nfunction getYear(date) {\n    if (date instanceof Date) {\n        return date.getFullYear();\n    }\n    if (typeof date === 'number') {\n        return date;\n    }\n    var year = parseInt(date, 10);\n    if (typeof date === 'string' && !isNaN(year)) {\n        return year;\n    }\n    throw new Error(\"Failed to get year from date: \".concat(date, \".\"));\n}\n/**\n * Gets month from a given date.\n *\n * @param {Date} date Date to get month from\n * @returns {number} Month\n */\nfunction getMonth(date) {\n    if (date instanceof Date) {\n        return date.getMonth();\n    }\n    throw new Error(\"Failed to get month from date: \".concat(date, \".\"));\n}\n/**\n * Gets human-readable month from a given date.\n *\n * @param {Date} date Date to get human-readable month from\n * @returns {number} Human-readable month\n */\nfunction getMonthHuman(date) {\n    if (date instanceof Date) {\n        return date.getMonth() + 1;\n    }\n    throw new Error(\"Failed to get human-readable month from date: \".concat(date, \".\"));\n}\n/**\n * Gets day of the month from a given date.\n *\n * @param {Date} date Date to get day of the month from\n * @returns {number} Day of the month\n */\nfunction getDate(date) {\n    if (date instanceof Date) {\n        return date.getDate();\n    }\n    throw new Error(\"Failed to get year from date: \".concat(date, \".\"));\n}\n/**\n * Gets hours from a given date.\n *\n * @param {Date | string} date Date to get hours from\n * @returns {number} Hours\n */\nfunction getHours(date) {\n    if (date instanceof Date) {\n        return date.getHours();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var hoursString = datePieces[0];\n            if (hoursString) {\n                var hours = parseInt(hoursString, 10);\n                if (!isNaN(hours)) {\n                    return hours;\n                }\n            }\n        }\n    }\n    throw new Error(\"Failed to get hours from date: \".concat(date, \".\"));\n}\n/**\n * Gets minutes from a given date.\n *\n * @param {Date | string} date Date to get minutes from\n * @returns {number} Minutes\n */\nfunction getMinutes(date) {\n    if (date instanceof Date) {\n        return date.getMinutes();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var minutesString = datePieces[1] || '0';\n            var minutes = parseInt(minutesString, 10);\n            if (!isNaN(minutes)) {\n                return minutes;\n            }\n        }\n    }\n    throw new Error(\"Failed to get minutes from date: \".concat(date, \".\"));\n}\n/**\n * Gets seconds from a given date.\n *\n * @param {Date | string} date Date to get seconds from\n * @returns {number} Seconds\n */\nfunction getSeconds(date) {\n    if (date instanceof Date) {\n        return date.getSeconds();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var secondsWithMillisecondsString = datePieces[2] || '0';\n            var seconds = parseInt(secondsWithMillisecondsString, 10);\n            if (!isNaN(seconds)) {\n                return seconds;\n            }\n        }\n    }\n    throw new Error(\"Failed to get seconds from date: \".concat(date, \".\"));\n}\n/**\n * Gets milliseconds from a given date.\n *\n * @param {Date | string} date Date to get milliseconds from\n * @returns {number} Milliseconds\n */\nfunction getMilliseconds(date) {\n    if (date instanceof Date) {\n        return date.getMilliseconds();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var secondsWithMillisecondsString = datePieces[2] || '0';\n            var millisecondsString = secondsWithMillisecondsString.split('.')[1] || '0';\n            var milliseconds = parseInt(millisecondsString, 10);\n            if (!isNaN(milliseconds)) {\n                return milliseconds;\n            }\n        }\n    }\n    throw new Error(\"Failed to get seconds from date: \".concat(date, \".\"));\n}\n/**\n * Century\n */\n/**\n * Gets century start date from a given date.\n *\n * @param {DateLike} date Date to get century start from\n * @returns {Date} Century start date\n */\nfunction getCenturyStart(date) {\n    var year = getYear(date);\n    var centuryStartYear = year + ((-year + 1) % 100);\n    var centuryStartDate = new Date();\n    centuryStartDate.setFullYear(centuryStartYear, 0, 1);\n    centuryStartDate.setHours(0, 0, 0, 0);\n    return centuryStartDate;\n}\n/**\n * Gets previous century start date from a given date.\n *\n * @param {DateLike} date Date to get previous century start from\n * @returns {Date} Previous century start date\n */\nvar getPreviousCenturyStart = makeGetEdgeOfNeighbor(getYear, getCenturyStart, -100);\n/**\n * Gets next century start date from a given date.\n *\n * @param {DateLike} date Date to get next century start from\n * @returns {Date} Next century start date\n */\nvar getNextCenturyStart = makeGetEdgeOfNeighbor(getYear, getCenturyStart, 100);\n/**\n * Gets century end date from a given date.\n *\n * @param {DateLike} date Date to get century end from\n * @returns {Date} Century end date\n */\nvar getCenturyEnd = makeGetEnd(getNextCenturyStart);\n/**\n * Gets previous century end date from a given date.\n *\n * @param {DateLike} date Date to get previous century end from\n * @returns {Date} Previous century end date\n */\nvar getPreviousCenturyEnd = makeGetEdgeOfNeighbor(getYear, getCenturyEnd, -100);\n/**\n * Gets next century end date from a given date.\n *\n * @param {DateLike} date Date to get next century end from\n * @returns {Date} Next century end date\n */\nvar getNextCenturyEnd = makeGetEdgeOfNeighbor(getYear, getCenturyEnd, 100);\n/**\n * Gets century start and end dates from a given date.\n *\n * @param {DateLike} date Date to get century start and end from\n * @returns {[Date, Date]} Century start and end dates\n */\nvar getCenturyRange = makeGetRange(getCenturyStart, getCenturyEnd);\n/**\n * Decade\n */\n/**\n * Gets decade start date from a given date.\n *\n * @param {DateLike} date Date to get decade start from\n * @returns {Date} Decade start date\n */\nfunction getDecadeStart(date) {\n    var year = getYear(date);\n    var decadeStartYear = year + ((-year + 1) % 10);\n    var decadeStartDate = new Date();\n    decadeStartDate.setFullYear(decadeStartYear, 0, 1);\n    decadeStartDate.setHours(0, 0, 0, 0);\n    return decadeStartDate;\n}\n/**\n * Gets previous decade start date from a given date.\n *\n * @param {DateLike} date Date to get previous decade start from\n * @returns {Date} Previous decade start date\n */\nvar getPreviousDecadeStart = makeGetEdgeOfNeighbor(getYear, getDecadeStart, -10);\n/**\n * Gets next decade start date from a given date.\n *\n * @param {DateLike} date Date to get next decade start from\n * @returns {Date} Next decade start date\n */\nvar getNextDecadeStart = makeGetEdgeOfNeighbor(getYear, getDecadeStart, 10);\n/**\n * Gets decade end date from a given date.\n *\n * @param {DateLike} date Date to get decade end from\n * @returns {Date} Decade end date\n */\nvar getDecadeEnd = makeGetEnd(getNextDecadeStart);\n/**\n * Gets previous decade end date from a given date.\n *\n * @param {DateLike} date Date to get previous decade end from\n * @returns {Date} Previous decade end date\n */\nvar getPreviousDecadeEnd = makeGetEdgeOfNeighbor(getYear, getDecadeEnd, -10);\n/**\n * Gets next decade end date from a given date.\n *\n * @param {DateLike} date Date to get next decade end from\n * @returns {Date} Next decade end date\n */\nvar getNextDecadeEnd = makeGetEdgeOfNeighbor(getYear, getDecadeEnd, 10);\n/**\n * Gets decade start and end dates from a given date.\n *\n * @param {DateLike} date Date to get decade start and end from\n * @returns {[Date, Date]} Decade start and end dates\n */\nvar getDecadeRange = makeGetRange(getDecadeStart, getDecadeEnd);\n/**\n * Year\n */\n/**\n * Gets year start date from a given date.\n *\n * @param {DateLike} date Date to get year start from\n * @returns {Date} Year start date\n */\nfunction getYearStart(date) {\n    var year = getYear(date);\n    var yearStartDate = new Date();\n    yearStartDate.setFullYear(year, 0, 1);\n    yearStartDate.setHours(0, 0, 0, 0);\n    return yearStartDate;\n}\n/**\n * Gets previous year start date from a given date.\n *\n * @param {DateLike} date Date to get previous year start from\n * @returns {Date} Previous year start date\n */\nvar getPreviousYearStart = makeGetEdgeOfNeighbor(getYear, getYearStart, -1);\n/**\n * Gets next year start date from a given date.\n *\n * @param {DateLike} date Date to get next year start from\n * @returns {Date} Next year start date\n */\nvar getNextYearStart = makeGetEdgeOfNeighbor(getYear, getYearStart, 1);\n/**\n * Gets year end date from a given date.\n *\n * @param {DateLike} date Date to get year end from\n * @returns {Date} Year end date\n */\nvar getYearEnd = makeGetEnd(getNextYearStart);\n/**\n * Gets previous year end date from a given date.\n *\n * @param {DateLike} date Date to get previous year end from\n * @returns {Date} Previous year end date\n */\nvar getPreviousYearEnd = makeGetEdgeOfNeighbor(getYear, getYearEnd, -1);\n/**\n * Gets next year end date from a given date.\n *\n * @param {DateLike} date Date to get next year end from\n * @returns {Date} Next year end date\n */\nvar getNextYearEnd = makeGetEdgeOfNeighbor(getYear, getYearEnd, 1);\n/**\n * Gets year start and end dates from a given date.\n *\n * @param {DateLike} date Date to get year start and end from\n * @returns {[Date, Date]} Year start and end dates\n */\nvar getYearRange = makeGetRange(getYearStart, getYearEnd);\n/**\n * Month\n */\nfunction makeGetEdgeOfNeighborMonth(getEdgeOfPeriod, defaultOffset) {\n    return function makeGetEdgeOfNeighborMonthInternal(date, offset) {\n        if (offset === void 0) { offset = defaultOffset; }\n        var year = getYear(date);\n        var month = getMonth(date) + offset;\n        var previousPeriod = new Date();\n        previousPeriod.setFullYear(year, month, 1);\n        previousPeriod.setHours(0, 0, 0, 0);\n        return getEdgeOfPeriod(previousPeriod);\n    };\n}\n/**\n * Gets month start date from a given date.\n *\n * @param {DateLike} date Date to get month start from\n * @returns {Date} Month start date\n */\nfunction getMonthStart(date) {\n    var year = getYear(date);\n    var month = getMonth(date);\n    var monthStartDate = new Date();\n    monthStartDate.setFullYear(year, month, 1);\n    monthStartDate.setHours(0, 0, 0, 0);\n    return monthStartDate;\n}\n/**\n * Gets previous month start date from a given date.\n *\n * @param {DateLike} date Date to get previous month start from\n * @returns {Date} Previous month start date\n */\nvar getPreviousMonthStart = makeGetEdgeOfNeighborMonth(getMonthStart, -1);\n/**\n * Gets next month start date from a given date.\n *\n * @param {DateLike} date Date to get next month start from\n * @returns {Date} Next month start date\n */\nvar getNextMonthStart = makeGetEdgeOfNeighborMonth(getMonthStart, 1);\n/**\n * Gets month end date from a given date.\n *\n * @param {DateLike} date Date to get month end from\n * @returns {Date} Month end date\n */\nvar getMonthEnd = makeGetEnd(getNextMonthStart);\n/**\n * Gets previous month end date from a given date.\n *\n * @param {DateLike} date Date to get previous month end from\n * @returns {Date} Previous month end date\n */\nvar getPreviousMonthEnd = makeGetEdgeOfNeighborMonth(getMonthEnd, -1);\n/**\n * Gets next month end date from a given date.\n *\n * @param {DateLike} date Date to get next month end from\n * @returns {Date} Next month end date\n */\nvar getNextMonthEnd = makeGetEdgeOfNeighborMonth(getMonthEnd, 1);\n/**\n * Gets month start and end dates from a given date.\n *\n * @param {DateLike} date Date to get month start and end from\n * @returns {[Date, Date]} Month start and end dates\n */\nvar getMonthRange = makeGetRange(getMonthStart, getMonthEnd);\n/**\n * Day\n */\nfunction makeGetEdgeOfNeighborDay(getEdgeOfPeriod, defaultOffset) {\n    return function makeGetEdgeOfNeighborDayInternal(date, offset) {\n        if (offset === void 0) { offset = defaultOffset; }\n        var year = getYear(date);\n        var month = getMonth(date);\n        var day = getDate(date) + offset;\n        var previousPeriod = new Date();\n        previousPeriod.setFullYear(year, month, day);\n        previousPeriod.setHours(0, 0, 0, 0);\n        return getEdgeOfPeriod(previousPeriod);\n    };\n}\n/**\n * Gets day start date from a given date.\n *\n * @param {DateLike} date Date to get day start from\n * @returns {Date} Day start date\n */\nfunction getDayStart(date) {\n    var year = getYear(date);\n    var month = getMonth(date);\n    var day = getDate(date);\n    var dayStartDate = new Date();\n    dayStartDate.setFullYear(year, month, day);\n    dayStartDate.setHours(0, 0, 0, 0);\n    return dayStartDate;\n}\n/**\n * Gets previous day start date from a given date.\n *\n * @param {DateLike} date Date to get previous day start from\n * @returns {Date} Previous day start date\n */\nvar getPreviousDayStart = makeGetEdgeOfNeighborDay(getDayStart, -1);\n/**\n * Gets next day start date from a given date.\n *\n * @param {DateLike} date Date to get next day start from\n * @returns {Date} Next day start date\n */\nvar getNextDayStart = makeGetEdgeOfNeighborDay(getDayStart, 1);\n/**\n * Gets day end date from a given date.\n *\n * @param {DateLike} date Date to get day end from\n * @returns {Date} Day end date\n */\nvar getDayEnd = makeGetEnd(getNextDayStart);\n/**\n * Gets previous day end date from a given date.\n *\n * @param {DateLike} date Date to get previous day end from\n * @returns {Date} Previous day end date\n */\nvar getPreviousDayEnd = makeGetEdgeOfNeighborDay(getDayEnd, -1);\n/**\n * Gets next day end date from a given date.\n *\n * @param {DateLike} date Date to get next day end from\n * @returns {Date} Next day end date\n */\nvar getNextDayEnd = makeGetEdgeOfNeighborDay(getDayEnd, 1);\n/**\n * Gets day start and end dates from a given date.\n *\n * @param {DateLike} date Date to get day start and end from\n * @returns {[Date, Date]} Day start and end dates\n */\nvar getDayRange = makeGetRange(getDayStart, getDayEnd);\n/**\n * Other\n */\n/**\n * Returns a number of days in a month of a given date.\n *\n * @param {Date} date Date\n * @returns {number} Number of days in a month\n */\nfunction getDaysInMonth(date) {\n    return getDate(getMonthEnd(date));\n}\nfunction padStart(num, val) {\n    if (val === void 0) { val = 2; }\n    var numStr = \"\".concat(num);\n    if (numStr.length >= val) {\n        return num;\n    }\n    return \"0000\".concat(numStr).slice(-val);\n}\n/**\n * Returns local hours and minutes (hh:mm).\n *\n * @param {Date | string} date Date to get hours and minutes from\n * @returns {string} Local hours and minutes\n */\nfunction getHoursMinutes(date) {\n    var hours = padStart(getHours(date));\n    var minutes = padStart(getMinutes(date));\n    return \"\".concat(hours, \":\").concat(minutes);\n}\n/**\n * Returns local hours, minutes and seconds (hh:mm:ss).\n *\n * @param {Date | string} date Date to get hours, minutes and seconds from\n * @returns {string} Local hours, minutes and seconds\n */\nfunction getHoursMinutesSeconds(date) {\n    var hours = padStart(getHours(date));\n    var minutes = padStart(getMinutes(date));\n    var seconds = padStart(getSeconds(date));\n    return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n}\n/**\n * Returns local month in ISO-like format (YYYY-MM).\n *\n * @param {Date} date Date to get month in ISO-like format from\n * @returns {string} Local month in ISO-like format\n */\nfunction getISOLocalMonth(date) {\n    var year = padStart(getYear(date), 4);\n    var month = padStart(getMonthHuman(date));\n    return \"\".concat(year, \"-\").concat(month);\n}\n/**\n * Returns local date in ISO-like format (YYYY-MM-DD).\n *\n * @param {Date} date Date to get date in ISO-like format from\n * @returns {string} Local date in ISO-like format\n */\nfunction getISOLocalDate(date) {\n    var year = padStart(getYear(date), 4);\n    var month = padStart(getMonthHuman(date));\n    var day = padStart(getDate(date));\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n}\n/**\n * Returns local date & time in ISO-like format (YYYY-MM-DDThh:mm:ss).\n *\n * @param {Date} date Date to get date & time in ISO-like format from\n * @returns {string} Local date & time in ISO-like format\n */\nfunction getISOLocalDateTime(date) {\n    return \"\".concat(getISOLocalDate(date), \"T\").concat(getHoursMinutesSeconds(date));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wojtekmaj/date-utils/dist/esm/index.js\n");

/***/ })

};
;