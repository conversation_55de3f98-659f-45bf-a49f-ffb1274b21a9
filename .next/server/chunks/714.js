exports.id=714,exports.ids=[714],exports.modules={2876:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,3724,23)),Promise.resolve().then(r.t.bind(r,5365,23)),Promise.resolve().then(r.t.bind(r,4900,23)),Promise.resolve().then(r.t.bind(r,4714,23)),Promise.resolve().then(r.t.bind(r,5392,23)),Promise.resolve().then(r.t.bind(r,8898,23))},3277:()=>{},9113:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>RootLayout,metadata:()=>l});var t=r(4656),a=r(177),i=r.n(a);r(5023);let l={title:"引越しTodo管理",description:"引越し時のタスクを効率的に管理するアプリケーション"};function RootLayout({children:e}){return t.jsx("html",{lang:"ja",children:t.jsx("body",{className:i().className,children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"引越しTodo管理"}),(0,t.jsxs)("nav",{className:"space-x-4",children:[t.jsx("a",{href:"/",className:"text-primary-600 hover:text-primary-700 font-medium",children:"一覧"}),t.jsx("a",{href:"/calendar",className:"text-primary-600 hover:text-primary-700 font-medium",children:"カレンダー"})]})]})})}),t.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e})]})})})}},5023:()=>{}};