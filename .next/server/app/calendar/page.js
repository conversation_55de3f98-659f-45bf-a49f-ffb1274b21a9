(()=>{var e={};e.id=329,e.ids=[329],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},5658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>g,tree:()=>c});var a=r(7096),s=r(6132),l=r(7284),n=r.n(l),o=r(2564),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let c=["",{children:["calendar",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7299)),"/Users/<USER>/Projects/MovingTodo/src/app/calendar/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9113)),"/Users/<USER>/Projects/MovingTodo/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Projects/MovingTodo/src/app/calendar/page.tsx"],x="/calendar/page",u={require:r,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/calendar/page",pathname:"/calendar",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1516:(e,t,r)=>{Promise.resolve().then(r.bind(r,2350))},2350:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>CalendarView});var a=r(784),s=r(3745),l=r.n(s),n=r(9885),o=r(34);function CalendarView(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)([]),[i,c]=(0,n.useState)(null),[d,x]=(0,n.useState)(!0);(0,n.useEffect)(()=>{fetchData()},[]);let fetchData=async()=>{try{let[e,r]=await Promise.all([fetch("/api/todos"),fetch("/api/holidays")]),a=await e.json();if(t(a.todos),r.ok){let e=await r.json();s(e.holidays||[])}}catch(e){console.error("データ取得エラー:",e)}finally{x(!1)}},formatDateString=e=>{let t=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return`${t}-${r}-${a}`},getTodosForDate=t=>{let r=formatDateString(t);return e.filter(e=>{if(!e.due_date)return!1;let t=new Date(e.due_date),a=formatDateString(t);return a===r})},getHolidayForDate=e=>{let t=formatDateString(e);return r.find(e=>{if(!e.date)return!1;let r=new Date(e.date),a=formatDateString(r);return a===t})},isWeekend=e=>{let t=e.getDay();return 0===t||6===t};if(d)return a.jsx("div",{className:"flex justify-center items-center py-8",children:a.jsx("div",{className:"text-gray-500",children:"読み込み中..."})});let u=i?getTodosForDate(i):[];return(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"カレンダー"}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"jsx-c265cfddff217e65 card",children:[a.jsx(l(),{id:"c265cfddff217e65",children:".react-calendar{width:100%;background:white;border:none;font-family:inherit}.react-calendar__tile{max-width:100%;text-align:left;line-height:1.2;height:100px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch;padding:2px;position:relative;border:1px solid#e5e7eb}.react-calendar__tile.holiday-tile{background-color:#fef2f2}.react-calendar__tile.weekend-tile{background-color:#f8fafc}.react-calendar__tile.has-todos{border-left:3px solid#3b82f6}.react-calendar__tile:enabled:hover,.react-calendar__tile:enabled:focus{background-color:#dbeafe}.react-calendar__tile--active{background:#3b82f6!important;color:white}.react-calendar__navigation button{color:#3b82f6;font-weight:bold;font-size:16px}.react-calendar__month-view__weekdays{text-align:center;text-transform:uppercase;font-weight:bold;font-size:.75em;color:#6b7280}.react-calendar__tile abbr{font-size:14px;font-weight:bold;color:#374151;text-decoration:none}.react-calendar__tile.holiday-tile abbr{color:#dc2626}.react-calendar__tile:nth-child(7n-1) abbr{color:#2563eb}.react-calendar__tile:nth-child(7n) abbr{color:#dc2626}"}),a.jsx(o.ZP,{onChange:e=>c(e),value:i,tileContent:({date:e,view:t})=>{if("month"!==t)return null;let r=getTodosForDate(e),s=getHolidayForDate(e);return(0,a.jsxs)("div",{className:"w-full h-full flex flex-col justify-start p-1",children:[s&&a.jsx("div",{className:"text-xs text-red-600 font-bold truncate mb-1",children:s.name}),r.length>0&&(0,a.jsxs)("div",{className:"flex flex-col gap-1",children:[r.slice(0,3).map(e=>a.jsx("div",{className:"text-xs p-1 rounded truncate",style:{backgroundColor:e.category?.color||"#3b82f6",color:"white"},title:`${e.title} - ${e.category?.name||""} - ${"completed"===e.status?"完了":"in_progress"===e.status?"進行中":"未着手"}`,children:e.title},e.id)),r.length>3&&(0,a.jsxs)("div",{className:"text-xs text-gray-600 text-center",children:["+",r.length-3,"件"]})]})]})},tileClassName:({date:e,view:t})=>{if("month"!==t)return"";let r=[],a=getHolidayForDate(e),s=isWeekend(e);a?r.push("holiday-tile"):s&&r.push("weekend-tile");let l=getTodosForDate(e);return l.length>0&&r.push("has-todos"),r.join(" ")},locale:"ja-JP"})]})}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:i?`${i.toLocaleDateString("ja-JP")}のTodo`:"日付を選択してください"}),i&&a.jsx("div",{className:"space-y-3",children:0===u.length?a.jsx("p",{className:"text-gray-500",children:"この日にTodoはありません"}):u.map(e=>(0,a.jsxs)("div",{className:"border-l-4 pl-3 py-2",style:{borderColor:e.category?.color||"#3b82f6"},children:[a.jsx("h4",{className:"font-medium",children:e.title}),e.description&&a.jsx("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[a.jsx("span",{children:e.category?.name}),a.jsx("span",{className:`px-2 py-1 rounded ${"completed"===e.status?"bg-green-100 text-green-800":"in_progress"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:"completed"===e.status?"完了":"in_progress"===e.status?"進行中":"未着手"})]})]},e.id))})]})]})]})}r(6648)},7299:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>CalendarPage});var a=r(4656),s=r(5153);let l=(0,s.createProxy)(String.raw`/Users/<USER>/Projects/MovingTodo/src/components/CalendarView.tsx`),{__esModule:n,$$typeof:o}=l,i=l.default;function CalendarPage(){return a.jsx(i,{})}}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[485,879,714],()=>__webpack_exec__(5658));module.exports=r})();