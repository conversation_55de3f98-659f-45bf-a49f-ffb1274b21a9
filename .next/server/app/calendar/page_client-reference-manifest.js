globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/calendar/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"1457":{"*":{"id":"8255","name":"*","chunks":[],"async":false}},"3170":{"*":{"id":"4714","name":"*","chunks":[],"async":false}},"3728":{"*":{"id":"3724","name":"*","chunks":[],"async":false}},"6403":{"*":{"id":"2350","name":"*","chunks":[],"async":false}},"6954":{"*":{"id":"4900","name":"*","chunks":[],"async":false}},"7264":{"*":{"id":"5392","name":"*","chunks":[],"async":false}},"8297":{"*":{"id":"8898","name":"*","chunks":[],"async":false}},"9928":{"*":{"id":"5365","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/client/components/app-router.js":{"id":3728,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/esm/client/components/app-router.js":{"id":3728,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/client/components/error-boundary.js":{"id":9928,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":9928,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/client/components/layout-router.js":{"id":6954,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/esm/client/components/layout-router.js":{"id":6954,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/client/components/not-found-boundary.js":{"id":3170,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":3170,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/client/components/render-from-template-context.js":{"id":7264,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":7264,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":8297,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":8297,"name":"*","chunks":[],"async":false},"/Users/<USER>/Projects/MovingTodo/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":1654,"name":"*","chunks":["185","static/chunks/app/layout-cb3437ec3cd1b482.js"],"async":false},"/Users/<USER>/Projects/MovingTodo/src/app/globals.css":{"id":2489,"name":"*","chunks":["185","static/chunks/app/layout-cb3437ec3cd1b482.js"],"async":false},"/Users/<USER>/Projects/MovingTodo/src/components/TodoList.tsx":{"id":1457,"name":"*","chunks":["931","static/chunks/app/page-1cfe295f479afa56.js"],"async":false},"/Users/<USER>/Projects/MovingTodo/src/components/CalendarView.tsx":{"id":6403,"name":"*","chunks":["386","static/chunks/386-6b30f73edca1dc37.js","329","static/chunks/app/calendar/page-7e9c3212edfd5814.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Projects/MovingTodo/src/app/_not-found":[],"/Users/<USER>/Projects/MovingTodo/src/app/layout":["static/css/60143e46d52c6446.css"],"/Users/<USER>/Projects/MovingTodo/src/app/page":[],"/Users/<USER>/Projects/MovingTodo/src/app/calendar/page":["static/css/c104401138f68e2c.css"]}}