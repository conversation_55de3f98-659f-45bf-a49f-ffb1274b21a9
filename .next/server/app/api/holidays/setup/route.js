"use strict";(()=>{var e={};e.id=894,e.ids=[894],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8678:e=>{e.exports=import("pg")},8081:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{headerHooks:()=>l,originalPathname:()=>y,requestAsyncStorage:()=>c,routeModule:()=>u,serverHooks:()=>p,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>h});var s=a(884),o=a(6132),n=a(3863),i=e([n]);n=(i.then?(await i)():i)[0];let u=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/holidays/setup/route",pathname:"/api/holidays/setup",filename:"route",bundlePath:"app/api/holidays/setup/route"},resolvedPagePath:"/Users/<USER>/Projects/MovingTodo/src/app/api/holidays/setup/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:p,headerHooks:l,staticGenerationBailout:h}=u,y="/api/holidays/setup/route";r()}catch(e){r(e)}})},3863:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{POST:()=>POST});var s=a(5798),o=a(9919),n=e([o]);async function POST(){try{return await (0,o.I)(`
      CREATE TABLE IF NOT EXISTS holidays (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `),await (0,o.I)(`
      INSERT INTO holidays (date, name) VALUES 
        ('2024-01-01', '元日'),
        ('2024-01-08', '成人の日'),
        ('2024-02-11', '建国記念の日'),
        ('2024-02-12', '建国記念の日 振替休日'),
        ('2024-02-23', '天皇誕生日'),
        ('2024-03-20', '春分の日'),
        ('2024-04-29', '昭和の日'),
        ('2024-05-03', '憲法記念日'),
        ('2024-05-04', 'みどりの日'),
        ('2024-05-05', 'こどもの日'),
        ('2024-05-06', 'こどもの日 振替休日'),
        ('2024-07-15', '海の日'),
        ('2024-08-11', '山の日'),
        ('2024-08-12', '山の日 振替休日'),
        ('2024-09-16', '敬老の日'),
        ('2024-09-22', '秋分の日'),
        ('2024-09-23', '秋分の日 振替休日'),
        ('2024-10-14', 'スポーツの日'),
        ('2024-11-03', '文化の日'),
        ('2024-11-04', '文化の日 振替休日'),
        ('2024-11-23', '勤労感謝の日'),
        ('2025-01-01', '元日'),
        ('2025-01-13', '成人の日'),
        ('2025-02-11', '建国記念の日'),
        ('2025-02-23', '天皇誕生日'),
        ('2025-02-24', '天皇誕生日 振替休日'),
        ('2025-03-20', '春分の日'),
        ('2025-04-29', '昭和の日'),
        ('2025-05-03', '憲法記念日'),
        ('2025-05-04', 'みどりの日'),
        ('2025-05-05', 'こどもの日'),
        ('2025-05-06', 'こどもの日 振替休日')
      ON CONFLICT (date) DO NOTHING;
    `),s.Z.json({success:!0,message:"祝日データベースセットアップが完了しました"})}catch(e){return console.error("祝日データベースセットアップエラー:",e),s.Z.json({success:!1,error:"祝日データベースセットアップに失敗しました",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}o=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},9919:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.d(t,{I:()=>query});var s=a(8678),o=e([s]);s=(o.then?(await o)():o)[0];let n=process.env.DATABASE_URL||"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",i=new s.Pool({connectionString:n,ssl:{rejectUnauthorized:!1}});async function query(e,t){let a=await i.connect();try{let r=await a.query(e,t);return r}finally{a.release()}}r()}catch(e){r(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[997],()=>__webpack_exec__(8081));module.exports=a})();