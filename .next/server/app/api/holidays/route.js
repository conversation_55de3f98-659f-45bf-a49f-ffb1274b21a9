"use strict";(()=>{var e={};e.id=846,e.ids=[846],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8678:e=>{e.exports=import("pg")},1810:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.r(a),t.d(a,{headerHooks:()=>p,originalPathname:()=>y,requestAsyncStorage:()=>u,routeModule:()=>l,serverHooks:()=>d,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>h});var o=t(884),s=t(6132),n=t(1509),i=e([n]);n=(i.then?(await i)():i)[0];let l=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/holidays/route",pathname:"/api/holidays",filename:"route",bundlePath:"app/api/holidays/route"},resolvedPagePath:"/Users/<USER>/Projects/MovingTodo/src/app/api/holidays/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:u,staticGenerationAsyncStorage:c,serverHooks:d,headerHooks:p,staticGenerationBailout:h}=l,y="/api/holidays/route";r()}catch(e){r(e)}})},1509:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.r(a),t.d(a,{GET:()=>GET});var o=t(5798),s=t(9919),n=e([s]);async function GET(){try{let e=await (0,s.I)(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'holidays'
      );
    `);if(!e.rows[0].exists)return o.Z.json({holidays:[]});let a=await (0,s.I)(`
      SELECT * FROM holidays 
      WHERE date >= CURRENT_DATE - INTERVAL '1 year'
      AND date <= CURRENT_DATE + INTERVAL '1 year'
      ORDER BY date
    `);return o.Z.json({holidays:a.rows})}catch(e){return console.error("祝日取得エラー:",e),o.Z.json({holidays:[]})}}s=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},9919:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{I:()=>query});var o=t(8678),s=e([o]);o=(s.then?(await s)():s)[0];let n=process.env.DATABASE_URL||"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",i=new o.Pool({connectionString:n,ssl:{rejectUnauthorized:!1}});async function query(e,a){let t=await i.connect();try{let r=await t.query(e,a);return r}finally{t.release()}}r()}catch(e){r(e)}})}};var a=require("../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),t=a.X(0,[997],()=>__webpack_exec__(1810));module.exports=t})();