"use strict";(()=>{var e={};e.id=227,e.ids=[227],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8678:e=>{e.exports=import("pg")},2024:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{headerHooks:()=>d,originalPathname:()=>h,requestAsyncStorage:()=>c,routeModule:()=>u,serverHooks:()=>l,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>g});var s=a(884),n=a(6132),o=a(5148),i=e([o]);o=(i.then?(await i)():i)[0];let u=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/assignees/route",pathname:"/api/assignees",filename:"route",bundlePath:"app/api/assignees/route"},resolvedPagePath:"/Users/<USER>/Projects/MovingTodo/src/app/api/assignees/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:c,staticGenerationAsyncStorage:p,serverHooks:l,headerHooks:d,staticGenerationBailout:g}=u,h="/api/assignees/route";r()}catch(e){r(e)}})},5148:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{GET:()=>GET});var s=a(5798),n=a(9919),o=e([n]);async function GET(){try{let e=await (0,n.I)(`
      SELECT * FROM assignees 
      ORDER BY name
    `);return s.Z.json({assignees:e.rows})}catch(e){return console.error("担当者取得エラー:",e),s.Z.json({error:"担当者の取得に失敗しました"},{status:500})}}n=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},9919:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.d(t,{I:()=>query});var s=a(8678),n=e([s]);s=(n.then?(await n)():n)[0];let o=process.env.DATABASE_URL||"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",i=new s.Pool({connectionString:o,ssl:{rejectUnauthorized:!1}});async function query(e,t){let a=await i.connect();try{let r=await a.query(e,t);return r}finally{a.release()}}r()}catch(e){r(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[997],()=>__webpack_exec__(2024));module.exports=a})();