"use strict";(()=>{var e={};e.id=110,e.ids=[110],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8678:e=>{e.exports=import("pg")},7033:(e,t,o)=>{o.a(e,async(e,r)=>{try{o.r(t),o.d(t,{headerHooks:()=>h,originalPathname:()=>y,requestAsyncStorage:()=>u,routeModule:()=>d,serverHooks:()=>c,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>l});var s=o(884),a=o(6132),i=o(8977),n=e([i]);i=(n.then?(await n)():n)[0];let d=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/todos/[id]/route",pathname:"/api/todos/[id]",filename:"route",bundlePath:"app/api/todos/[id]/route"},resolvedPagePath:"/Users/<USER>/Projects/MovingTodo/src/app/api/todos/[id]/route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:u,staticGenerationAsyncStorage:p,serverHooks:c,headerHooks:h,staticGenerationBailout:l}=d,y="/api/todos/[id]/route";r()}catch(e){r(e)}})},8977:(e,t,o)=>{o.a(e,async(e,r)=>{try{o.r(t),o.d(t,{DELETE:()=>DELETE,PATCH:()=>PATCH});var s=o(5798),a=o(9919),i=e([a]);async function PATCH(e,{params:t}){try{let o=parseInt(t.id),r=await e.json(),i=[],n=[],d=1;void 0!==r.title&&(i.push(`title = $${d}`),n.push(r.title),d++),void 0!==r.description&&(i.push(`description = $${d}`),n.push(r.description),d++),void 0!==r.due_date&&(i.push(`due_date = $${d}`),n.push(r.due_date),d++),void 0!==r.category_id&&(i.push(`category_id = $${d}`),n.push(r.category_id),d++),void 0!==r.assignee_id&&(i.push(`assignee_id = $${d}`),n.push(r.assignee_id),d++),void 0!==r.status&&(i.push(`status = $${d}`),n.push(r.status),d++),void 0!==r.priority&&(i.push(`priority = $${d}`),n.push(r.priority),d++),i.push("updated_at = NOW()"),n.push(o);let u=await (0,a.I)(`
      UPDATE todos 
      SET ${i.join(", ")}
      WHERE id = $${d}
      RETURNING *
    `,n);if(0===u.rows.length)return s.Z.json({error:"Todoが見つかりません"},{status:404});return s.Z.json({todo:u.rows[0],message:"Todoを更新しました"})}catch(e){return console.error("Todo更新エラー:",e),s.Z.json({error:"Todoの更新に失敗しました"},{status:500})}}async function DELETE(e,{params:t}){try{let e=parseInt(t.id),o=await (0,a.I)(`
      DELETE FROM todos WHERE id = $1
      RETURNING *
    `,[e]);if(0===o.rows.length)return s.Z.json({error:"Todoが見つかりません"},{status:404});return s.Z.json({message:"Todoを削除しました"})}catch(e){return console.error("Todo削除エラー:",e),s.Z.json({error:"Todoの削除に失敗しました"},{status:500})}}a=(i.then?(await i)():i)[0],r()}catch(e){r(e)}})},9919:(e,t,o)=>{o.a(e,async(e,r)=>{try{o.d(t,{I:()=>query});var s=o(8678),a=e([s]);s=(a.then?(await a)():a)[0];let i=process.env.DATABASE_URL||"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",n=new s.Pool({connectionString:i,ssl:{rejectUnauthorized:!1}});async function query(e,t){let o=await n.connect();try{let r=await o.query(e,t);return r}finally{o.release()}}r()}catch(e){r(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),o=t.X(0,[997],()=>__webpack_exec__(7033));module.exports=o})();