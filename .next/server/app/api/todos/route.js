"use strict";(()=>{var e={};e.id=603,e.ids=[603],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8678:e=>{e.exports=import("pg")},8954:(e,t,a)=>{a.a(e,async(e,o)=>{try{a.r(t),a.d(t,{headerHooks:()=>_,originalPathname:()=>g,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>l,staticGenerationAsyncStorage:()=>u,staticGenerationBailout:()=>p});var r=a(884),s=a(6132),i=a(6819),n=e([i]);i=(n.then?(await n)():n)[0];let d=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/todos/route",pathname:"/api/todos",filename:"route",bundlePath:"app/api/todos/route"},resolvedPagePath:"/Users/<USER>/Projects/MovingTodo/src/app/api/todos/route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:c,staticGenerationAsyncStorage:u,serverHooks:l,headerHooks:_,staticGenerationBailout:p}=d,g="/api/todos/route";o()}catch(e){o(e)}})},6819:(e,t,a)=>{a.a(e,async(e,o)=>{try{a.r(t),a.d(t,{GET:()=>GET,POST:()=>POST});var r=a(5798),s=a(9919),i=e([s]);async function GET(){try{let e=await (0,s.I)(`
      SELECT 
        t.*,
        c.name as category_name,
        c.color as category_color,
        a.name as assignee_name
      FROM todos t
      LEFT JOIN categories c ON t.category_id = c.id
      LEFT JOIN assignees a ON t.assignee_id = a.id
      ORDER BY t.due_date ASC NULLS LAST, t.created_at DESC
    `),t=e.rows.map(e=>({id:e.id,title:e.title,description:e.description,due_date:e.due_date,category_id:e.category_id,assignee_id:e.assignee_id,status:e.status,priority:e.priority,created_at:e.created_at,updated_at:e.updated_at,category:e.category_name?{id:e.category_id,name:e.category_name,color:e.category_color}:null,assignee:e.assignee_name?{id:e.assignee_id,name:e.assignee_name}:null}));return r.Z.json({todos:t})}catch(e){return console.error("Todo取得エラー:",e),r.Z.json({error:"Todoの取得に失敗しました"},{status:500})}}async function POST(e){try{let t=await e.json(),a=await (0,s.I)(`
      INSERT INTO todos (title, description, due_date, category_id, assignee_id, priority)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `,[t.title,t.description||null,t.due_date||null,t.category_id||null,t.assignee_id||null,t.priority||1]);return r.Z.json({todo:a.rows[0],message:"Todoを作成しました"})}catch(e){return console.error("Todo作成エラー:",e),r.Z.json({error:"Todoの作成に失敗しました"},{status:500})}}s=(i.then?(await i)():i)[0],o()}catch(e){o(e)}})},9919:(e,t,a)=>{a.a(e,async(e,o)=>{try{a.d(t,{I:()=>query});var r=a(8678),s=e([r]);r=(s.then?(await s)():s)[0];let i=process.env.DATABASE_URL||"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",n=new r.Pool({connectionString:i,ssl:{rejectUnauthorized:!1}});async function query(e,t){let a=await n.connect();try{let o=await a.query(e,t);return o}finally{a.release()}}o()}catch(e){o(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[997],()=>__webpack_exec__(8954));module.exports=a})();