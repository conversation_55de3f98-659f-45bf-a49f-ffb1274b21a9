"use strict";(()=>{var e={};e.id=961,e.ids=[961],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8678:e=>{e.exports=import("pg")},4872:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{headerHooks:()=>d,originalPathname:()=>h,requestAsyncStorage:()=>u,routeModule:()=>c,serverHooks:()=>l,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>g});var o=r(884),n=r(6132),s=r(4765),i=e([s]);s=(i.then?(await i)():i)[0];let c=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"/Users/<USER>/Projects/MovingTodo/src/app/api/categories/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:u,staticGenerationAsyncStorage:p,serverHooks:l,headerHooks:d,staticGenerationBailout:g}=c,h="/api/categories/route";a()}catch(e){a(e)}})},4765:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GET:()=>GET});var o=r(5798),n=r(9919),s=e([n]);async function GET(){try{let e=await (0,n.I)(`
      SELECT * FROM categories 
      ORDER BY name
    `);return o.Z.json({categories:e.rows})}catch(e){return console.error("カテゴリー取得エラー:",e),o.Z.json({error:"カテゴリーの取得に失敗しました"},{status:500})}}n=(s.then?(await s)():s)[0],a()}catch(e){a(e)}})},9919:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{I:()=>query});var o=r(8678),n=e([o]);o=(n.then?(await n)():n)[0];let s=process.env.DATABASE_URL||"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",i=new o.Pool({connectionString:s,ssl:{rejectUnauthorized:!1}});async function query(e,t){let r=await i.connect();try{let a=await r.query(e,t);return a}finally{r.release()}}a()}catch(e){a(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[997],()=>__webpack_exec__(4872));module.exports=r})();