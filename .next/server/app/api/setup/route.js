"use strict";(()=>{var e={};e.id=838,e.ids=[838],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8678:e=>{e.exports=import("pg")},2016:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{headerHooks:()=>u,originalPathname:()=>A,requestAsyncStorage:()=>T,routeModule:()=>n,serverHooks:()=>N,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>R});var s=a(884),E=a(6132),o=a(9721),i=e([o]);o=(i.then?(await i)():i)[0];let n=new s.AppRouteRouteModule({definition:{kind:E.x.APP_ROUTE,page:"/api/setup/route",pathname:"/api/setup",filename:"route",bundlePath:"app/api/setup/route"},resolvedPagePath:"/Users/<USER>/Projects/MovingTodo/src/app/api/setup/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:T,staticGenerationAsyncStorage:d,serverHooks:N,headerHooks:u,staticGenerationBailout:R}=n,A="/api/setup/route";r()}catch(e){r(e)}})},9721:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{POST:()=>POST});var s=a(5798),E=a(9919),o=e([E]);async function POST(){try{return await (0,E.I)(`
      CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        color VARCHAR(7) NOT NULL DEFAULT '#3b82f6',
        created_at TIMESTAMP DEFAULT NOW()
      );
    `),await (0,E.I)(`
      CREATE TABLE IF NOT EXISTS assignees (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `),await (0,E.I)(`
      CREATE TABLE IF NOT EXISTS todos (
        id SERIAL PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        due_date DATE,
        category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
        assignee_id INTEGER REFERENCES assignees(id) ON DELETE SET NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed')),
        priority INTEGER DEFAULT 1 CHECK (priority IN (1, 2, 3)),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `),await (0,E.I)(`
      CREATE TABLE IF NOT EXISTS holidays (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `),await (0,E.I)(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql';
    `),await (0,E.I)(`
      DROP TRIGGER IF EXISTS update_todos_updated_at ON todos;
      CREATE TRIGGER update_todos_updated_at 
        BEFORE UPDATE ON todos 
        FOR EACH ROW 
        EXECUTE FUNCTION update_updated_at_column();
    `),await (0,E.I)(`
      INSERT INTO categories (name, color) VALUES 
        ('手続き', '#ef4444'),
        ('荷造り', '#f59e0b'),
        ('清掃', '#10b981'),
        ('引越し業者', '#3b82f6'),
        ('新居準備', '#8b5cf6'),
        ('ライフライン', '#f97316')
      ON CONFLICT DO NOTHING;
    `),await (0,E.I)(`
      INSERT INTO assignees (name) VALUES 
        ('自分'),
        ('家族'),
        ('業者')
      ON CONFLICT DO NOTHING;
    `),await (0,E.I)(`
      INSERT INTO holidays (date, name) VALUES 
        ('2024-01-01', '元日'),
        ('2024-01-08', '成人の日'),
        ('2024-02-11', '建国記念の日'),
        ('2024-02-12', '建国記念の日 振替休日'),
        ('2024-02-23', '天皇誕生日'),
        ('2024-03-20', '春分の日'),
        ('2024-04-29', '昭和の日'),
        ('2024-05-03', '憲法記念日'),
        ('2024-05-04', 'みどりの日'),
        ('2024-05-05', 'こどもの日'),
        ('2024-05-06', 'こどもの日 振替休日'),
        ('2024-07-15', '海の日'),
        ('2024-08-11', '山の日'),
        ('2024-08-12', '山の日 振替休日'),
        ('2024-09-16', '敬老の日'),
        ('2024-09-22', '秋分の日'),
        ('2024-09-23', '秋分の日 振替休日'),
        ('2024-10-14', 'スポーツの日'),
        ('2024-11-03', '文化の日'),
        ('2024-11-04', '文化の日 振替休日'),
        ('2024-11-23', '勤労感謝の日'),
        ('2025-01-01', '元日'),
        ('2025-01-13', '成人の日'),
        ('2025-02-11', '建国記念の日'),
        ('2025-02-23', '天皇誕生日'),
        ('2025-02-24', '天皇誕生日 振替休日'),
        ('2025-03-20', '春分の日'),
        ('2025-04-29', '昭和の日'),
        ('2025-05-03', '憲法記念日'),
        ('2025-05-04', 'みどりの日'),
        ('2025-05-05', 'こどもの日'),
        ('2025-05-06', 'こどもの日 振替休日')
      ON CONFLICT (date) DO NOTHING;
    `),s.Z.json({success:!0,message:"データベースセットアップが完了しました（祝日データも追加）"})}catch(e){return console.error("データベースセットアップエラー:",e),s.Z.json({success:!1,error:"データベースセットアップに失敗しました",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}E=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},9919:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.d(t,{I:()=>query});var s=a(8678),E=e([s]);s=(E.then?(await E)():E)[0];let o=process.env.DATABASE_URL||"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",i=new s.Pool({connectionString:o,ssl:{rejectUnauthorized:!1}});async function query(e,t){let a=await i.connect();try{let r=await a.query(e,t);return r}finally{a.release()}}r()}catch(e){r(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[997],()=>__webpack_exec__(2016));module.exports=a})();