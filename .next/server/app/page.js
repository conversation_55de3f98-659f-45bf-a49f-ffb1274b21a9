(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},7768:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(7096),r=s(6132),o=s(7284),n=s.n(o),i=s(2564),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1964)),"/Users/<USER>/Projects/MovingTodo/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,9113)),"/Users/<USER>/Projects/MovingTodo/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Projects/MovingTodo/src/app/page.tsx"],u="/page",x={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},345:(e,t,s)=>{Promise.resolve().then(s.bind(s,8255))},8255:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>TodoList});var a=s(784),r=s(9885);function TodoItem({todo:e,onUpdate:t,onEdit:s,onDelete:r}){let updateStatus=async s=>{try{let a=await fetch(`/api/todos/${e.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:s})});a.ok&&t()}catch(e){console.error("ステータス更新エラー:",e)}},handleDelete=async()=>{if(window.confirm("このTodoを削除してもよろしいですか？"))try{let s=await fetch(`/api/todos/${e.id}`,{method:"DELETE"});s.ok&&t()}catch(e){console.error("削除エラー:",e)}};return(0,a.jsxs)("div",{className:"card hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("span",{className:`px-2 py-1 rounded text-xs font-medium ${(e=>{switch(e){case 3:return"bg-red-100 text-red-800";case 2:return"bg-orange-100 text-orange-800";default:return"bg-blue-100 text-blue-800"}})(e.priority)}`,children:["優先度",(e=>{switch(e){case 3:return"高";case 2:return"中";default:return"低"}})(e.priority)]}),a.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${(e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"in_progress":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(e.status)}`,children:(e=>{switch(e){case"completed":return"完了";case"in_progress":return"進行中";default:return"未着手"}})(e.status)})]})]}),e.description&&a.jsx("p",{className:"text-gray-600 mb-3",children:e.description}),a.jsx("div",{className:"flex justify-between items-center text-sm text-gray-500 mb-4",children:(0,a.jsxs)("div",{className:"space-x-4",children:[e.due_date&&(0,a.jsxs)("span",{children:["期日: ",(e=>{if(!e)return null;let t=new Date(e);return t.toLocaleDateString("ja-JP")})(e.due_date)]}),e.category&&a.jsx("span",{className:"px-2 py-1 rounded text-xs font-medium text-white",style:{backgroundColor:e.category.color},children:e.category.name}),e.assignee&&(0,a.jsxs)("span",{children:["担当: ",e.assignee.name]})]})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:["completed"!==e.status&&a.jsx("button",{onClick:()=>updateStatus("pending"===e.status?"in_progress":"completed"),className:"text-sm bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded transition-colors",children:"pending"===e.status?"開始":"完了"}),"completed"===e.status&&a.jsx("button",{onClick:()=>updateStatus("in_progress"),className:"text-sm bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition-colors",children:"再開"}),a.jsx("button",{onClick:()=>s&&s(e),className:"text-sm bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded transition-colors",children:"編集"}),a.jsx("button",{onClick:handleDelete,className:"text-sm bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded transition-colors",children:"削除"})]})]})}function TodoForm({onClose:e,onSuccess:t,editTodo:s}){let[o,n]=(0,r.useState)(""),[i,l]=(0,r.useState)(""),[d,c]=(0,r.useState)(""),[u,x]=(0,r.useState)(""),[p,m]=(0,r.useState)(""),[h,g]=(0,r.useState)(1),[y,j]=(0,r.useState)([]),[b,f]=(0,r.useState)([]),[v,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{fetchMasterData(),s&&(n(s.title),l(s.description||""),c(s.due_date?s.due_date.split("T")[0]:""),x(s.category_id?.toString()||""),m(s.assignee_id?.toString()||""),g(s.priority))},[s]);let fetchMasterData=async()=>{try{let[e,t]=await Promise.all([fetch("/api/categories"),fetch("/api/assignees")]),s=await e.json(),a=await t.json();j(s.categories),f(a.assignees)}catch(e){console.error("マスターデータ取得エラー:",e)}},handleSubmit=async e=>{e.preventDefault(),N(!0);try{let e=s?`/api/todos/${s.id}`:"/api/todos",a=s?"PATCH":"POST",r=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify({title:o,description:i||void 0,due_date:d||void 0,category_id:u?parseInt(u):void 0,assignee_id:p?parseInt(p):void 0,priority:h})});r.ok?t():console.error(s?"Todo更新エラー":"Todo作成エラー")}catch(e){console.error(s?"Todo更新エラー:":"Todo作成エラー:",e)}finally{N(!1)}};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h3",{className:"text-xl font-semibold",children:s?"Todoを編集":"新しいTodo"}),a.jsx("button",{onClick:e,className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),(0,a.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"form-label",children:"タイトル *"}),a.jsx("input",{type:"text",value:o,onChange:e=>n(e.target.value),className:"form-input",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"form-label",children:"説明"}),a.jsx("textarea",{value:i,onChange:e=>l(e.target.value),className:"form-input",rows:3})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"form-label",children:"期日"}),a.jsx("input",{type:"date",value:d,onChange:e=>c(e.target.value),className:"form-input"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"form-label",children:"カテゴリー"}),(0,a.jsxs)("select",{value:u,onChange:e=>x(e.target.value),className:"form-input",children:[a.jsx("option",{value:"",children:"選択してください"}),y.map(e=>a.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"form-label",children:"担当者"}),(0,a.jsxs)("select",{value:p,onChange:e=>m(e.target.value),className:"form-input",children:[a.jsx("option",{value:"",children:"選択してください"}),b.map(e=>a.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"form-label",children:"優先度"}),(0,a.jsxs)("select",{value:h,onChange:e=>g(parseInt(e.target.value)),className:"form-input",children:[a.jsx("option",{value:1,children:"低"}),a.jsx("option",{value:2,children:"中"}),a.jsx("option",{value:3,children:"高"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[a.jsx("button",{type:"button",onClick:e,className:"btn-secondary",children:"キャンセル"}),a.jsx("button",{type:"submit",disabled:v||!o,className:"btn-primary disabled:opacity-50",children:v?s?"更新中...":"作成中...":s?"更新":"作成"})]})]})]})}function TodoList(){let[e,t]=(0,r.useState)([]),[s,o]=(0,r.useState)(!0),[n,i]=(0,r.useState)(!1),[l,d]=(0,r.useState)(null),[c,u]=(0,r.useState)("all");(0,r.useEffect)(()=>{fetchTodos()},[]);let fetchTodos=async()=>{try{let e=await fetch("/api/todos"),s=await e.json();t(s.todos)}catch(e){console.error("Todo取得エラー:",e)}finally{o(!1)}},handleEditTodo=e=>{d(e),i(!0)},x=e.filter(e=>"all"===c||e.status===c);return s?a.jsx("div",{className:"flex justify-center items-center py-8",children:a.jsx("div",{className:"text-gray-500",children:"読み込み中..."})}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Todoリスト"}),a.jsx("button",{onClick:()=>i(!0),className:"btn-primary",children:"新しいTodoを追加"})]}),a.jsx("div",{className:"flex space-x-2 mb-6",children:[{key:"all",label:"すべて"},{key:"pending",label:"未着手"},{key:"in_progress",label:"進行中"},{key:"completed",label:"完了"}].map(e=>a.jsx("button",{onClick:()=>u(e.key),className:`px-4 py-2 rounded-lg font-medium transition-colors ${c===e.key?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:e.label},e.key))})]}),n&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:a.jsx("div",{className:"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:a.jsx(TodoForm,{onClose:()=>{i(!1),d(null)},onSuccess:()=>{i(!1),d(null),fetchTodos()},editTodo:l||void 0})})}),a.jsx("div",{className:"space-y-4",children:0===x.length?a.jsx("div",{className:"text-center py-8 text-gray-500",children:"all"===c?"Todoがありません":`${"pending"===c?"未着手":"in_progress"===c?"進行中":"完了"}のTodoがありません`}):x.map(e=>a.jsx(TodoItem,{todo:e,onUpdate:fetchTodos,onEdit:handleEditTodo},e.id))})]})}},1964:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Home});var a=s(4656),r=s(5153);let o=(0,r.createProxy)(String.raw`/Users/<USER>/Projects/MovingTodo/src/components/TodoList.tsx`),{__esModule:n,$$typeof:i}=o,l=o.default;function Home(){return a.jsx(l,{})}},784:(e,t,s)=>{"use strict";e.exports=s(316).vendored["react-ssr"].ReactJsxRuntime}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[485,714],()=>__webpack_exec__(7768));module.exports=s})();