(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{1462:function(e,t,s){Promise.resolve().then(s.bind(s,1457))},1457:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return TodoList}});var a=s(7437),n=s(2265);function TodoItem(e){let{todo:t,onUpdate:s,onEdit:n,onDelete:r}=e,updateStatus=async e=>{try{let a=await fetch("/api/todos/".concat(t.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:e})});a.ok&&s()}catch(e){console.error("ステータス更新エラー:",e)}},handleDelete=async()=>{if(window.confirm("このTodoを削除してもよろしいですか？"))try{let e=await fetch("/api/todos/".concat(t.id),{method:"DELETE"});e.ok&&s()}catch(e){console.error("削除エラー:",e)}};return(0,a.jsxs)("div",{className:"card hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t.title}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat((e=>{switch(e){case 3:return"bg-red-100 text-red-800";case 2:return"bg-orange-100 text-orange-800";default:return"bg-blue-100 text-blue-800"}})(t.priority)),children:["優先度",(e=>{switch(e){case 3:return"高";case 2:return"中";default:return"低"}})(t.priority)]}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat((e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"in_progress":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(t.status)),children:(e=>{switch(e){case"completed":return"完了";case"in_progress":return"進行中";default:return"未着手"}})(t.status)})]})]}),t.description&&(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:t.description}),(0,a.jsx)("div",{className:"flex justify-between items-center text-sm text-gray-500 mb-4",children:(0,a.jsxs)("div",{className:"space-x-4",children:[t.due_date&&(0,a.jsxs)("span",{children:["期日: ",(e=>{if(!e)return null;let t=new Date(e);return t.toLocaleDateString("ja-JP")})(t.due_date)]}),t.category&&(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium text-white",style:{backgroundColor:t.category.color},children:t.category.name}),t.assignee&&(0,a.jsxs)("span",{children:["担当: ",t.assignee.name]})]})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:["completed"!==t.status&&(0,a.jsx)("button",{onClick:()=>updateStatus("pending"===t.status?"in_progress":"completed"),className:"text-sm bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded transition-colors",children:"pending"===t.status?"開始":"完了"}),"completed"===t.status&&(0,a.jsx)("button",{onClick:()=>updateStatus("in_progress"),className:"text-sm bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition-colors",children:"再開"}),(0,a.jsx)("button",{onClick:()=>n&&n(t),className:"text-sm bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded transition-colors",children:"編集"}),(0,a.jsx)("button",{onClick:handleDelete,className:"text-sm bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded transition-colors",children:"削除"})]})]})}function TodoForm(e){let{onClose:t,onSuccess:s,editTodo:r}=e,[o,l]=(0,n.useState)(""),[i,c]=(0,n.useState)(""),[d,u]=(0,n.useState)(""),[x,m]=(0,n.useState)(""),[h,p]=(0,n.useState)(""),[f,y]=(0,n.useState)(1),[g,b]=(0,n.useState)([]),[j,v]=(0,n.useState)([]),[N,w]=(0,n.useState)(!1);(0,n.useEffect)(()=>{if(fetchMasterData(),r){var e,t;l(r.title),c(r.description||""),u(r.due_date?r.due_date.split("T")[0]:""),m((null===(e=r.category_id)||void 0===e?void 0:e.toString())||""),p((null===(t=r.assignee_id)||void 0===t?void 0:t.toString())||""),y(r.priority)}},[r]);let fetchMasterData=async()=>{try{let[e,t]=await Promise.all([fetch("/api/categories"),fetch("/api/assignees")]),s=await e.json(),a=await t.json();b(s.categories),v(a.assignees)}catch(e){console.error("マスターデータ取得エラー:",e)}},handleSubmit=async e=>{e.preventDefault(),w(!0);try{let e=r?"/api/todos/".concat(r.id):"/api/todos",t=r?"PATCH":"POST",a=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify({title:o,description:i||void 0,due_date:d||void 0,category_id:x?parseInt(x):void 0,assignee_id:h?parseInt(h):void 0,priority:f})});a.ok?s():console.error(r?"Todo更新エラー":"Todo作成エラー")}catch(e){console.error(r?"Todo更新エラー:":"Todo作成エラー:",e)}finally{w(!1)}};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:r?"Todoを編集":"新しいTodo"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),(0,a.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"form-label",children:"タイトル *"}),(0,a.jsx)("input",{type:"text",value:o,onChange:e=>l(e.target.value),className:"form-input",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"form-label",children:"説明"}),(0,a.jsx)("textarea",{value:i,onChange:e=>c(e.target.value),className:"form-input",rows:3})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"form-label",children:"期日"}),(0,a.jsx)("input",{type:"date",value:d,onChange:e=>u(e.target.value),className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"form-label",children:"カテゴリー"}),(0,a.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"form-input",children:[(0,a.jsx)("option",{value:"",children:"選択してください"}),g.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"form-label",children:"担当者"}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"form-input",children:[(0,a.jsx)("option",{value:"",children:"選択してください"}),j.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"form-label",children:"優先度"}),(0,a.jsxs)("select",{value:f,onChange:e=>y(parseInt(e.target.value)),className:"form-input",children:[(0,a.jsx)("option",{value:1,children:"低"}),(0,a.jsx)("option",{value:2,children:"中"}),(0,a.jsx)("option",{value:3,children:"高"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"btn-secondary",children:"キャンセル"}),(0,a.jsx)("button",{type:"submit",disabled:N||!o,className:"btn-primary disabled:opacity-50",children:N?r?"更新中...":"作成中...":r?"更新":"作成"})]})]})]})}function TodoList(){let[e,t]=(0,n.useState)([]),[s,r]=(0,n.useState)(!0),[o,l]=(0,n.useState)(!1),[i,c]=(0,n.useState)(null),[d,u]=(0,n.useState)("all");(0,n.useEffect)(()=>{fetchTodos()},[]);let fetchTodos=async()=>{try{let e=await fetch("/api/todos"),s=await e.json();t(s.todos)}catch(e){console.error("Todo取得エラー:",e)}finally{r(!1)}},handleEditTodo=e=>{c(e),l(!0)},x=e.filter(e=>"all"===d||e.status===d);return s?(0,a.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,a.jsx)("div",{className:"text-gray-500",children:"読み込み中..."})}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Todoリスト"}),(0,a.jsx)("button",{onClick:()=>l(!0),className:"btn-primary",children:"新しいTodoを追加"})]}),(0,a.jsx)("div",{className:"flex space-x-2 mb-6",children:[{key:"all",label:"すべて"},{key:"pending",label:"未着手"},{key:"in_progress",label:"進行中"},{key:"completed",label:"完了"}].map(e=>(0,a.jsx)("button",{onClick:()=>u(e.key),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat(d===e.key?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:e.label},e.key))})]}),o&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsx)(TodoForm,{onClose:()=>{l(!1),c(null)},onSuccess:()=>{l(!1),c(null),fetchTodos()},editTodo:i||void 0})})}),(0,a.jsx)("div",{className:"space-y-4",children:0===x.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"all"===d?"Todoがありません":"".concat("pending"===d?"未着手":"in_progress"===d?"進行中":"完了","のTodoがありません")}):x.map(e=>(0,a.jsx)(TodoItem,{todo:e,onUpdate:fetchTodos,onEdit:handleEditTodo},e.id))})]})}},622:function(e,t,s){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=s(2265),n=Symbol.for("react.element"),r=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),o=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var a,i={},c=null,d=null;for(a in void 0!==s&&(c=""+s),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)r.call(t,a)&&!l.hasOwnProperty(a)&&(i[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===i[a]&&(i[a]=t[a]);return{$$typeof:n,type:e,key:c,ref:d,props:i,_owner:o.current}}t.jsx=q,t.jsxs=q},7437:function(e,t,s){"use strict";e.exports=s(622)}},function(e){e.O(0,[971,472,744],function(){return e(e.s=1462)}),_N_E=e.O()}]);