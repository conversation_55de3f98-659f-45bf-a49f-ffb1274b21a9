(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[329],{3139:function(e,t,a){Promise.resolve().then(a.bind(a,6403))},6403:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return CalendarView}});var l=a(7437),r=a(9738),n=a.n(r),i=a(2265),o=a(569);function CalendarView(){let[e,t]=(0,i.useState)([]),[a,r]=(0,i.useState)([]),[s,c]=(0,i.useState)(null),[d,x]=(0,i.useState)(!0);(0,i.useEffect)(()=>{fetchData()},[]);let fetchData=async()=>{try{let[e,a]=await Promise.all([fetch("/api/todos"),fetch("/api/holidays")]),l=await e.json();if(t(l.todos),a.ok){let e=await a.json();r(e.holidays||[])}}catch(e){console.error("データ取得エラー:",e)}finally{x(!1)}},formatDateString=e=>{let t=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),l=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(a,"-").concat(l)},getTodosForDate=t=>{let a=formatDateString(t);return e.filter(e=>{if(!e.due_date)return!1;let t=new Date(e.due_date),l=formatDateString(t);return l===a})},getHolidayForDate=e=>{let t=formatDateString(e);return a.find(e=>{if(!e.date)return!1;let a=new Date(e.date),l=formatDateString(a);return l===t})},isWeekend=e=>{let t=e.getDay();return 0===t||6===t};if(d)return(0,l.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,l.jsx)("div",{className:"text-gray-500",children:"読み込み中..."})});let f=s?getTodosForDate(s):[];return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"カレンダー"}),(0,l.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,l.jsx)("div",{className:"lg:col-span-2",children:(0,l.jsxs)("div",{className:"jsx-c265cfddff217e65 card",children:[(0,l.jsx)(n(),{id:"c265cfddff217e65",children:".react-calendar{width:100%;background:white;border:none;font-family:inherit}.react-calendar__tile{max-width:100%;text-align:left;line-height:1.2;height:100px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch;padding:2px;position:relative;border:1px solid#e5e7eb}.react-calendar__tile.holiday-tile{background-color:#fef2f2}.react-calendar__tile.weekend-tile{background-color:#f8fafc}.react-calendar__tile.has-todos{border-left:3px solid#3b82f6}.react-calendar__tile:enabled:hover,.react-calendar__tile:enabled:focus{background-color:#dbeafe}.react-calendar__tile--active{background:#3b82f6!important;color:white}.react-calendar__navigation button{color:#3b82f6;font-weight:bold;font-size:16px}.react-calendar__month-view__weekdays{text-align:center;text-transform:uppercase;font-weight:bold;font-size:.75em;color:#6b7280}.react-calendar__tile abbr{font-size:14px;font-weight:bold;color:#374151;text-decoration:none}.react-calendar__tile.holiday-tile abbr{color:#dc2626}.react-calendar__tile:nth-child(7n-1) abbr{color:#2563eb}.react-calendar__tile:nth-child(7n) abbr{color:#dc2626}"}),(0,l.jsx)(o.ZP,{onChange:e=>c(e),value:s,tileContent:e=>{let{date:t,view:a}=e;if("month"!==a)return null;let r=getTodosForDate(t),n=getHolidayForDate(t);return(0,l.jsxs)("div",{className:"w-full h-full flex flex-col justify-start p-1",children:[n&&(0,l.jsx)("div",{className:"text-xs text-red-600 font-bold truncate mb-1",children:n.name}),r.length>0&&(0,l.jsxs)("div",{className:"flex flex-col gap-1",children:[r.slice(0,3).map(e=>{var t,a;return(0,l.jsx)("div",{className:"text-xs p-1 rounded truncate",style:{backgroundColor:(null===(t=e.category)||void 0===t?void 0:t.color)||"#3b82f6",color:"white"},title:"".concat(e.title," - ").concat((null===(a=e.category)||void 0===a?void 0:a.name)||""," - ").concat("completed"===e.status?"完了":"in_progress"===e.status?"進行中":"未着手"),children:e.title},e.id)}),r.length>3&&(0,l.jsxs)("div",{className:"text-xs text-gray-600 text-center",children:["+",r.length-3,"件"]})]})]})},tileClassName:e=>{let{date:t,view:a}=e;if("month"!==a)return"";let l=[],r=getHolidayForDate(t),n=isWeekend(t);r?l.push("holiday-tile"):n&&l.push("weekend-tile");let i=getTodosForDate(t);return i.length>0&&l.push("has-todos"),l.join(" ")},locale:"ja-JP"})]})}),(0,l.jsxs)("div",{className:"card",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:s?"".concat(s.toLocaleDateString("ja-JP"),"のTodo"):"日付を選択してください"}),s&&(0,l.jsx)("div",{className:"space-y-3",children:0===f.length?(0,l.jsx)("p",{className:"text-gray-500",children:"この日にTodoはありません"}):f.map(e=>{var t,a;return(0,l.jsxs)("div",{className:"border-l-4 pl-3 py-2",style:{borderColor:(null===(t=e.category)||void 0===t?void 0:t.color)||"#3b82f6"},children:[(0,l.jsx)("h4",{className:"font-medium",children:e.title}),e.description&&(0,l.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,l.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[(0,l.jsx)("span",{children:null===(a=e.category)||void 0===a?void 0:a.name}),(0,l.jsx)("span",{className:"px-2 py-1 rounded ".concat("completed"===e.status?"bg-green-100 text-green-800":"in_progress"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:"completed"===e.status?"完了":"in_progress"===e.status?"進行中":"未着手"})]})]},e.id)})})]})]})]})}a(8094)}},function(e){e.O(0,[386,971,472,744],function(){return e(e.s=3139)}),_N_E=e.O()}]);